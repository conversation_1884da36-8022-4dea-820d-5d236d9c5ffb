import { useEffect, useState, useCallback } from 'react';
import socketService from '@/lib/socket';
import { SocketNewBidData, SocketConnectionStatus, SocketBidData } from '@/lib/types';
import { timeSyncService, TimeSyncStatus } from '@/lib/timeSync';

export const useSocket = () => {
  const [connectionStatus, setConnectionStatus] = useState<SocketConnectionStatus>({
    connected: false,
  });
  const [timeSyncStatus, setTimeSyncStatus] = useState<TimeSyncStatus>({
    synchronized: false,
    offset: 0,
    lastSync: 0,
  });

  useEffect(() => {
    // Connect to socket when hook is used
    const socket = socketService.connect();

    const handleConnect = () => {
      console.log('Socket connected in useSocket hook');
      setConnectionStatus({ connected: true });
    };

    const handleDisconnect = (reason: string) => {
      console.log('Socket disconnected in useSocket hook:', reason);
      setConnectionStatus({
        connected: false,
        error: `Disconnected: ${reason}`
      });
      // Stop periodic sync when disconnected
      timeSyncService.stopSync();
    };

    const handleConnectError = (error: any) => {
      console.error('Socket connection error in useSocket hook:', error);
      setConnectionStatus({
        connected: false,
        error: `Connection error: ${error.message || error}`
      });
    };

    // Set up event listeners
    socket.on('connect', handleConnect);
    socket.on('disconnect', handleDisconnect);
    socket.on('connect_error', handleConnectError);

    // Set initial connection status
    setConnectionStatus({ connected: socket.connected });

    // Cleanup on unmount
    return () => {
      socket.off('connect', handleConnect);
      socket.off('disconnect', handleDisconnect);
      socket.off('connect_error', handleConnectError);
    };
  }, []);

  // Time sync effect
  useEffect(() => {
    const handleTimeSyncUpdate = (status: TimeSyncStatus) => {
      setTimeSyncStatus(status);
      setConnectionStatus(prev => ({
        ...prev,
        timeSynced: status.synchronized,
        timeOffset: status.offset,
      }));
    };

    timeSyncService.addListener(handleTimeSyncUpdate);

    // Set initial status
    setTimeSyncStatus(timeSyncService.getStatus());

    return () => {
      timeSyncService.removeListener(handleTimeSyncUpdate);
    };
  }, []);

  const subscribeToAuction = useCallback((auctionId: string) => {
    socketService.subscribeToAuction(auctionId);
  }, []);

  const unsubscribeFromAuction = useCallback((auctionId: string) => {
    socketService.unsubscribeFromAuction(auctionId);
  }, []);

  const placeBid = useCallback((bidData: SocketBidData) => {
    socketService.placeBid(bidData);
  }, []);

  const onNewBid = useCallback((callback: (data: SocketNewBidData) => void) => {
    socketService.onNewBid(callback);
    
    // Return cleanup function
    return () => {
      socketService.offNewBid(callback);
    };
  }, []);

  const onAuctionUpdate = useCallback((callback: (data: any) => void) => {
    socketService.onAuctionUpdate(callback);

    // Return cleanup function
    return () => {
      socketService.offAuctionUpdate(callback);
    };
  }, []);

  const onBidError = useCallback((callback: (data: { message: string }) => void) => {
    socketService.onBidError(callback);

    // Return cleanup function
    return () => {
      socketService.offBidError(callback);
    };
  }, []);

  const getServerTime = useCallback(() => {
    return socketService.getServerTime();
  }, []);

  const isTimeSynchronized = useCallback(() => {
    return socketService.isTimeSynchronized();
  }, []);

  const performTimeSync = useCallback(() => {
    socketService.performTimeSync();
  }, []);

  return {
    connectionStatus,
    timeSyncStatus,
    subscribeToAuction,
    unsubscribeFromAuction,
    placeBid,
    onNewBid,
    onAuctionUpdate,
    onBidError,
    getServerTime,
    isTimeSynchronized,
    performTimeSync,
    isConnected: connectionStatus.connected,
  };
};

export default useSocket;
