export interface Product {
  id: string;
  name: string;
  description: string;
  category: string;
  startingPrice: number;
  currentBid: number;
  imageUrl: string;
  condition: string;
  brand?: string;
}

export interface LiveReel {
  id: string;
  title: string;
  thumbnailUrl: string;
  videoUrl: string;
  product: Product;
  auctioneer: {
    name: string;
    avatar: string;
    rating: number;
  };
  viewerCount: number;
  endTime: Date;
  isLive: boolean;
  bidCount: number;
}

export interface Bid {
  id: string;
  reelId: string;
  amount: number;
  bidder: {
    name: string;
    avatar: string;
    flightNumber?: string;
  };
  timestamp: Date;
  serverTime?: number; // Server timestamp for synchronization
}

export interface BidHistory {
  reelId: string;
  bids: Bid[];
}

// Socket.IO related types
export interface SocketBidData {
  auctionId: string;
  username: string;
  price: number;
  flightNumber?: string;
}

export interface SocketNewBidData {
  bid: {
    id: string;
    auctionId: string;
    amount: number;
    bidder: {
      name: string;
      avatar: string;
      flightNumber?: string;
    };
    timestamp: string;
    serverTime?: number;
  };
  serverTime?: number;
}

export interface SocketAuctionUpdate {
  auctionId: string;
  currentBid: number;
  bidCount: number;
  serverTime?: number;
}

// Time synchronization types
export interface TimeSyncRequest {
  clientTime: number;
}

export interface TimeSyncResponse {
  clientTime: number;
  serverTime: number;
  roundTripTime: number;
}

export interface SocketConnectionStatus {
  connected: boolean;
  error?: string;
  timeSynced?: boolean;
  timeOffset?: number;
}


